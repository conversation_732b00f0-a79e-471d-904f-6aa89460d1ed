// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all features
    initNavigation();
    initThemeToggle();
    initParticles();
    initScrollAnimations();
    initSkillBars();
    initCounters();
    initContactForm();
    initTypingEffect();
    initProjectImages();
    initProfileImage();
});

// Navigation functionality
function initNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    hamburger.addEventListener('click', () => {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Active navigation link on scroll
    window.addEventListener('scroll', () => {
        let current = '';
        const sections = document.querySelectorAll('section');
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (scrollY >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href').substring(1) === current) {
                link.classList.add('active');
            }
        });

        // Navbar background on scroll (theme-aware)
        updateNavbarOnScroll();
    });
}

// Theme Toggle functionality
function initThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');
    const body = document.body;

    // Check for saved theme preference or default to light mode
    const currentTheme = localStorage.getItem('theme') || 'light';
    body.setAttribute('data-theme', currentTheme);

    // Update toggle button icon based on current theme
    updateThemeIcon(currentTheme);

    // Theme toggle event listener
    themeToggle.addEventListener('click', () => {
        const currentTheme = body.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        // Apply new theme
        body.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        // Update icon
        updateThemeIcon(newTheme);

        // Update navbar background for theme
        updateNavbarForTheme(newTheme);
    });

    // Initial navbar update
    updateNavbarForTheme(currentTheme);
}

function updateThemeIcon(theme) {
    const themeToggle = document.getElementById('themeToggle');
    const icon = themeToggle.querySelector('i');

    if (theme === 'dark') {
        icon.className = 'fas fa-sun';
    } else {
        icon.className = 'fas fa-moon';
    }
}

function updateNavbarForTheme(theme) {
    // Initial navbar background based on theme
    updateNavbarOnScroll();
}

function updateNavbarOnScroll() {
    const navbar = document.querySelector('.navbar');
    const currentTheme = document.body.getAttribute('data-theme');

    if (window.scrollY > 100) {
        if (currentTheme === 'dark') {
            navbar.style.background = 'rgba(26, 32, 44, 0.98)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        }
    } else {
        if (currentTheme === 'dark') {
            navbar.style.background = 'rgba(26, 32, 44, 0.95)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        }
    }
}

// Particle system for hero section
function initParticles() {
    const canvas = document.getElementById('particles-canvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas size
    function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // Particle class
    class Particle {
        constructor() {
            this.x = Math.random() * canvas.width;
            this.y = Math.random() * canvas.height;
            this.size = Math.random() * 3 + 1;
            this.speedX = Math.random() * 2 - 1;
            this.speedY = Math.random() * 2 - 1;
            this.opacity = Math.random() * 0.5 + 0.2;
        }
        
        update() {
            this.x += this.speedX;
            this.y += this.speedY;
            
            if (this.x > canvas.width) this.x = 0;
            if (this.x < 0) this.x = canvas.width;
            if (this.y > canvas.height) this.y = 0;
            if (this.y < 0) this.y = canvas.height;
        }
        
        draw() {
            ctx.fillStyle = `rgba(255, 255, 255, ${this.opacity})`;
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
            ctx.fill();
        }
    }
    
    // Create particles
    const particles = [];
    const particleCount = 100;
    
    for (let i = 0; i < particleCount; i++) {
        particles.push(new Particle());
    }
    
    // Animation loop
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        particles.forEach(particle => {
            particle.update();
            particle.draw();
        });
        
        // Connect nearby particles
        for (let i = 0; i < particles.length; i++) {
            for (let j = i + 1; j < particles.length; j++) {
                const dx = particles[i].x - particles[j].x;
                const dy = particles[i].y - particles[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < 100) {
                    ctx.strokeStyle = `rgba(255, 255, 255, ${0.1 * (1 - distance / 100)})`;
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.moveTo(particles[i].x, particles[i].y);
                    ctx.lineTo(particles[j].x, particles[j].y);
                    ctx.stroke();
                }
            }
        }
        
        requestAnimationFrame(animate);
    }
    
    animate();
}

// Scroll animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);
    
    // Add animation classes to elements
    const animatedElements = document.querySelectorAll('.section-header, .about-text, .about-image, .project-card, .skill-category, .contact-item, .contact-form');
    
    animatedElements.forEach((el, index) => {
        if (index % 2 === 0) {
            el.classList.add('fade-in');
        } else {
            el.classList.add('slide-in-left');
        }
        observer.observe(el);
    });
}

// Animated skill bars
function initSkillBars() {
    const skillBars = document.querySelectorAll('.skill-progress');
    
    const skillObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const skillBar = entry.target;
                const width = skillBar.getAttribute('data-width');
                setTimeout(() => {
                    skillBar.style.width = width + '%';
                }, 500);
            }
        });
    }, { threshold: 0.5 });
    
    skillBars.forEach(bar => {
        skillObserver.observe(bar);
    });
}

// Animated counters
function initCounters() {
    const counters = document.querySelectorAll('.stat-number');
    
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / 100;
                let current = 0;
                
                const updateCounter = () => {
                    if (current < target) {
                        current += increment;
                        counter.textContent = Math.ceil(current);
                        setTimeout(updateCounter, 20);
                    } else {
                        counter.textContent = target;
                    }
                };
                
                updateCounter();
            }
        });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

// Contact form functionality
function initContactForm() {
    const form = document.querySelector('.contact-form');
    const submitButton = form.querySelector('button[type="submit"]');

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(form);
        const feedbackData = {
            name: formData.get('name'),
            email: formData.get('email'),
            subject: formData.get('subject'),
            message: formData.get('message')
        };

        // Simple validation
        if (!feedbackData.name || !feedbackData.email || !feedbackData.subject || !feedbackData.message) {
            showNotification('Please fill in all fields', 'error');
            return;
        }

        if (!isValidEmail(feedbackData.email)) {
            showNotification('Please enter a valid email address', 'error');
            return;
        }

        // Show loading state
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Sending...';
        submitButton.disabled = true;

        try {
            // Check if Firebase is available
            if (typeof window.FirebaseUtils !== 'undefined') {
                // Submit to Firebase
                const result = await window.FirebaseUtils.submitFeedback(feedbackData);

                if (result.success) {
                    showNotification('Thank you! Your message has been sent successfully!', 'success');
                    form.reset();
                } else {
                    throw new Error(result.error || 'Failed to send message');
                }
            } else {
                // Fallback: Log to console and show success message
                console.log('Firebase not configured. Feedback data:', feedbackData);
                showNotification('Message received! (Firebase not configured)', 'success');
                form.reset();
            }
        } catch (error) {
            console.error('Error submitting feedback:', error);
            showNotification('Sorry, there was an error sending your message. Please try again.', 'error');
        } finally {
            // Reset button state
            submitButton.textContent = originalText;
            submitButton.disabled = false;
        }
    });
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Notification system
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        border-radius: 12px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        transform: translateX(400px);
        transition: all 0.3s ease;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        min-width: 300px;
        font-family: 'Poppins', sans-serif;
        ${type === 'success' ? 'background: linear-gradient(135deg, #10b981, #059669);' : 'background: linear-gradient(135deg, #ef4444, #dc2626);'}
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Typing effect for hero section
function initTypingEffect() {
    const typingText = document.querySelector('.typing-text');
    const texts = ['Hello, I\'m', 'Namaste, I\'m', 'Hi there, I\'m'];
    let textIndex = 0;
    let charIndex = 0;
    let isDeleting = false;
    
    function typeEffect() {
        const currentText = texts[textIndex];
        
        if (isDeleting) {
            typingText.textContent = currentText.substring(0, charIndex - 1);
            charIndex--;
        } else {
            typingText.textContent = currentText.substring(0, charIndex + 1);
            charIndex++;
        }
        
        let typeSpeed = isDeleting ? 50 : 100;
        
        if (!isDeleting && charIndex === currentText.length) {
            typeSpeed = 2000;
            isDeleting = true;
        } else if (isDeleting && charIndex === 0) {
            isDeleting = false;
            textIndex = (textIndex + 1) % texts.length;
            typeSpeed = 500;
        }
        
        setTimeout(typeEffect, typeSpeed);
    }
    
    typeEffect();
}

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Project images functionality
function initProjectImages() {
    const projectImages = document.querySelectorAll('.project-img');

    projectImages.forEach(img => {
        // Create placeholder content
        const placeholder = createImagePlaceholder(img.alt, img.src);

        // Handle image load error
        img.addEventListener('error', function() {
            this.style.display = 'none';
            this.parentElement.appendChild(placeholder);
        });

        // Check if image exists
        img.addEventListener('load', function() {
            // Image loaded successfully
            const existingPlaceholder = this.parentElement.querySelector('.image-placeholder');
            if (existingPlaceholder) {
                existingPlaceholder.remove();
            }
        });

        // If image src is empty or doesn't exist, show placeholder immediately
        if (!img.src || img.src.includes('undefined')) {
            img.style.display = 'none';
            img.parentElement.appendChild(placeholder);
        }
    });
}

function createImagePlaceholder(altText, src) {
    const placeholder = document.createElement('div');
    placeholder.className = 'image-placeholder';

    // Determine icon and color based on project
    let icon = '📷';
    let gradient = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

    if (src.includes('plant-disease')) {
        icon = '🌱';
        gradient = 'linear-gradient(135deg, #4ade80 0%, #22c55e 100%)';
    } else if (src.includes('krishisahayak')) {
        icon = '🐄';
        gradient = 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)';
    } else if (src.includes('empathy-bot')) {
        icon = '🤖';
        gradient = 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)';
    } else if (src.includes('mentor-verse')) {
        icon = '👨‍🏫';
        gradient = 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)';
    }

    placeholder.style.cssText = `
        width: 100%;
        height: 100%;
        background: ${gradient};
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        position: absolute;
        top: 0;
        left: 0;
        transition: all 0.3s ease;
    `;

    placeholder.innerHTML = `
        <div style="font-size: 3rem; margin-bottom: 0.5rem;">${icon}</div>
        <div style="font-size: 0.9rem; font-weight: 500; text-align: center; opacity: 0.9;">${altText}</div>
        <div style="font-size: 0.7rem; opacity: 0.7; margin-top: 0.25rem;">Add project image</div>
    `;

    return placeholder;
}

// Profile Image functionality
function initProfileImage() {
    const profileImg = document.getElementById('profileImg');

    console.log('Initializing profile image...');
    console.log('Current src:', profileImg.src);
    console.log('Image element:', profileImg);

    // Test if image loads
    const testImg = new Image();
    testImg.onload = function() {
        console.log('✅ Profile image exists and loads successfully');
        profileImg.style.objectFit = 'cover';
    };
    testImg.onerror = function() {
        console.log('❌ Profile image failed to load, switching to placeholder');
        profileImg.src = 'profile-placeholder.svg';
    };
    testImg.src = 'profile.jpg?' + Date.now(); // Add cache buster

    // Handle image load error - fallback to placeholder
    profileImg.addEventListener('error', function() {
        console.log('Profile image error event triggered');
        this.src = 'profile-placeholder.svg';
    });

    // Handle successful image load
    profileImg.addEventListener('load', function() {
        console.log('Profile image load event triggered');
        this.style.objectFit = 'cover';
    });

    // Add click handler for future image upload functionality
    profileImg.addEventListener('click', function() {
        console.log('Profile image clicked - current src:', this.src);
    });
}

// Add loading animation
window.addEventListener('load', function() {
    const loader = document.getElementById('loader');
    setTimeout(() => {
        loader.classList.add('hidden');
        document.body.classList.add('loaded');
    }, 1000);
});

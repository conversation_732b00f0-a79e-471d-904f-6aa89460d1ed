// Firebase Configuration
// Replace these values with your actual Firebase project configuration
// const firebaseConfig = {
//     apiKey: "your-api-key-here",
//     authDomain: "your-project-id.firebaseapp.com",
//     projectId: "your-project-id",
//     storageBucket: "your-project-id.appspot.com",
//     messagingSenderId: "your-sender-id",
//     appId: "your-app-id"
// };
const firebaseConfig = {
    apiKey: "AIzaSyDHYtn8lYRMaC4qxInFGqszwKdBC0w_sMs",
    authDomain: "pf-feedback.firebaseapp.com",
    projectId: "pf-feedback",
    storageBucket: "pf-feedback.firebasestorage.app",
    messagingSenderId: "259535280060",
    appId: "1:259535280060:web:c40a1935a9783bc8e77db9",
    measurementId: "G-H4E737PTNF"
  };
// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firestore
const db = firebase.firestore();

// Function to submit feedback to Firebase
async function submitFeedback(formData) {
    try {
        // Add timestamp
        const feedbackData = {
            name: formData.name,
            email: formData.email,
            subject: formData.subject,
            message: formData.message,
            timestamp: firebase.firestore.FieldValue.serverTimestamp(),
            status: 'unread'
        };

        // Add document to Firestore
        const docRef = await db.collection('feedback').add(feedbackData);
        
        console.log('Feedback submitted with ID: ', docRef.id);
        return { success: true, id: docRef.id };
        
    } catch (error) {
        console.error('Error submitting feedback: ', error);
        return { success: false, error: error.message };
    }
}

// Function to get all feedback (for admin use)
async function getAllFeedback() {
    try {
        const snapshot = await db.collection('feedback')
            .orderBy('timestamp', 'desc')
            .get();
        
        const feedback = [];
        snapshot.forEach(doc => {
            feedback.push({
                id: doc.id,
                ...doc.data()
            });
        });
        
        return feedback;
    } catch (error) {
        console.error('Error getting feedback: ', error);
        return [];
    }
}

// Function to mark feedback as read
async function markFeedbackAsRead(feedbackId) {
    try {
        await db.collection('feedback').doc(feedbackId).update({
            status: 'read',
            readAt: firebase.firestore.FieldValue.serverTimestamp()
        });
        return { success: true };
    } catch (error) {
        console.error('Error marking feedback as read: ', error);
        return { success: false, error: error.message };
    }
}

// Export functions for use in main.js
window.FirebaseUtils = {
    submitFeedback,
    getAllFeedback,
    markFeedbackAsRead
};

// Chat Widget Functionality
class ChatWidget {
    constructor() {
        this.chatToggle = document.getElementById('chatToggle');
        this.chatWidget = document.getElementById('chatWidget');
        this.chatClose = document.getElementById('chatClose');
        this.chatInput = document.getElementById('chatInput');
        this.chatSend = document.getElementById('chatSend');
        this.chatMessages = document.getElementById('chatMessages');
        this.chatNotification = document.getElementById('chatNotification');

        this.isOpen = false;
        this.messageCount = 1; // Initial welcome message
        this.isFirstOpen = true; // Track if it's the first time opening

        // Your personal information
        this.myFullName = "Aditya Chaudhary";
        this.myDescription = `I'm <PERSON><PERSON><PERSON>, a B.Tech Computer Science & Engineering student at Heritage Institute of Technology, Kolkata, with a CGPA of 9.2+. I'm a passionate deep learning and ML enthusiast who loves building impactful projects that solve real-world problems. I have experience in Python, TensorFlow, Machine Learning, Deep Learning, Web Development, and Android Development. I've won the Hactonix Hackathon, was 2nd Runner-Up at Hack Heritage, and was a finalist in Smart India Hackathon 2024. My notable projects include Plant Disease Detection using deep learning, KrishiSahayak (livestock disease detection app), Empathy Bot (AI chatbot for introverts), and MentorVerse (mentor-mentee platform). I enjoy leading technical teams and am always eager to learn new technologies. You can reach <NAME_EMAIL> or connect with me on GitHub and LinkedIn.`;

        // Gemini API configuration
        this.GEMINI_API_KEY = "AIzaSyBQq2kl-llragFDJ9XfNGGTpA7849KLJ-8"; // Replace with your actual API key
        this.GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";

        this.init();
    }
    
    init() {
        // Event listeners
        this.chatToggle.addEventListener('click', () => this.toggleChat());
        this.chatClose.addEventListener('click', () => this.closeChat());
        this.chatSend.addEventListener('click', () => this.sendMessage());
        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });
        
        // Hide notification initially if no new messages
        this.updateNotification();
    }
    
    toggleChat() {
        if (this.isOpen) {
            this.closeChat();
        } else {
            this.openChat();
        }
    }
    
    openChat() {
        this.chatWidget.classList.add('active');
        this.isOpen = true;
        this.chatInput.focus();

        // Hide notification when chat is opened
        this.chatNotification.style.display = 'none';

        // Update toggle button icon
        this.chatToggle.innerHTML = '<i class="fas fa-times"></i>';

        // Show personalized initial message on first open
        if (this.isFirstOpen) {
            this.showInitialMessage();
            this.isFirstOpen = false;
        }
    }

    showInitialMessage() {
        // Clear existing messages first
        this.chatMessages.innerHTML = '';

        // Add personalized initial message
        const initialMessage = `Hi! I am ${this.myFullName}, what do you want to know about me?`;
        this.addMessage(initialMessage, 'bot');
    }
    
    closeChat() {
        this.chatWidget.classList.remove('active');
        this.isOpen = false;
        
        // Update toggle button icon
        this.chatToggle.innerHTML = '<i class="fas fa-comments"></i>';
        
        // Show notification if there are unread messages
        this.updateNotification();
    }
    
    async sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message) return;

        // Disable send button and input while processing
        this.chatSend.disabled = true;
        this.chatInput.disabled = true;

        // Add user message
        this.addMessage(message, 'user');
        this.chatInput.value = '';

        // Show typing animation
        this.showTypingAnimation();

        try {
            // Get response from Gemini API
            const response = await this.getGeminiResponse(message);
            this.hideTypingAnimation();
            this.addMessage(response, 'bot');
        } catch (error) {
            console.error('Error getting response:', error);
            this.hideTypingAnimation();
            this.addMessage('Sorry, I encountered an error. Please try again later.', 'bot');
        } finally {
            // Re-enable send button and input
            this.chatSend.disabled = false;
            this.chatInput.disabled = false;
            this.chatInput.focus();
        }
    }
    
    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <p>${text}</p>
                <span class="message-time">${currentTime}</span>
            </div>
        `;
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
        
        // Update message count for notifications
        if (sender === 'bot' && !this.isOpen) {
            this.messageCount++;
            this.updateNotification();
        }
    }
    
    async getGeminiResponse(message) {
        const prompt = `User_message: ${message}. Reply naturally to the user message and if required then answer based on: ${this.myDescription} or just simply give friendly reply. And reply in a way that ${this.myFullName} is himself talking. Reply in short sentences.`;

        const requestBody = {
            contents: [{
                parts: [{"text": prompt}]
            }]
        };

        const response = await fetch(`${this.GEMINI_API_URL}?key=${this.GEMINI_API_KEY}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Extract the response text from Gemini API response
        if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts[0]) {
            return data.candidates[0].content.parts[0].text;
        } else {
            throw new Error('Invalid response format from Gemini API');
        }
    }

    showTypingAnimation() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot-message typing-message';
        typingDiv.id = 'typingAnimation';

        typingDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;

        this.chatMessages.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingAnimation() {
        const typingAnimation = document.getElementById('typingAnimation');
        if (typingAnimation) {
            typingAnimation.remove();
        }
    }
    
    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
    
    updateNotification() {
        if (this.messageCount > 1 && !this.isOpen) {
            this.chatNotification.textContent = this.messageCount - 1;
            this.chatNotification.style.display = 'flex';
        } else {
            this.chatNotification.style.display = 'none';
        }
    }
    

}

// Initialize chat widget when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ChatWidget();
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatWidget;
}

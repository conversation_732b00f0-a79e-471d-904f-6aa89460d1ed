// Chat Widget Functionality
class ChatWidget {
    constructor() {
        this.chatToggle = document.getElementById('chatToggle');
        this.chatWidget = document.getElementById('chatWidget');
        this.chatClose = document.getElementById('chatClose');
        this.chatInput = document.getElementById('chatInput');
        this.chatSend = document.getElementById('chatSend');
        this.chatMessages = document.getElementById('chatMessages');
        this.chatNotification = document.getElementById('chatNotification');
        
        this.isOpen = false;
        this.messageCount = 1; // Initial welcome message
        
        this.init();
    }
    
    init() {
        // Event listeners
        this.chatToggle.addEventListener('click', () => this.toggleChat());
        this.chatClose.addEventListener('click', () => this.closeChat());
        this.chatSend.addEventListener('click', () => this.sendMessage());
        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });
        
        // Hide notification initially if no new messages
        this.updateNotification();
    }
    
    toggleChat() {
        if (this.isOpen) {
            this.closeChat();
        } else {
            this.openChat();
        }
    }
    
    openChat() {
        this.chatWidget.classList.add('active');
        this.isOpen = true;
        this.chatInput.focus();
        
        // Hide notification when chat is opened
        this.chatNotification.style.display = 'none';
        
        // Update toggle button icon
        this.chatToggle.innerHTML = '<i class="fas fa-times"></i>';
    }
    
    closeChat() {
        this.chatWidget.classList.remove('active');
        this.isOpen = false;
        
        // Update toggle button icon
        this.chatToggle.innerHTML = '<i class="fas fa-comments"></i>';
        
        // Show notification if there are unread messages
        this.updateNotification();
    }
    
    sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message) return;
        
        // Add user message
        this.addMessage(message, 'user');
        this.chatInput.value = '';
        
        // Simulate bot response (you can replace this with actual chatbot integration)
        setTimeout(() => {
            this.addBotResponse(message);
        }, 1000);
    }
    
    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <p>${text}</p>
                <span class="message-time">${currentTime}</span>
            </div>
        `;
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
        
        // Update message count for notifications
        if (sender === 'bot' && !this.isOpen) {
            this.messageCount++;
            this.updateNotification();
        }
    }
    
    addBotResponse(userMessage) {
        // Simple response logic - you can replace this with actual AI integration
        let response = this.generateResponse(userMessage.toLowerCase());
        this.addMessage(response, 'bot');
    }
    
    generateResponse(message) {
        // Simple keyword-based responses - replace with actual chatbot API
        const responses = {
            'hello': 'Hello! How can I help you today?',
            'hi': 'Hi there! What would you like to know about Aditya?',
            'projects': 'Aditya has worked on several exciting projects including Plant Disease Detection, KrishiSahayak, Empathy Bot, and MentorVerse. Which one interests you?',
            'skills': 'Aditya specializes in Python, Machine Learning, Deep Learning, TensorFlow, and web development. He also has experience with Android development and various databases.',
            'contact': 'You can reach <NAME_EMAIL> or call +91 7992466682. He\'s based in Kolkata, India.',
            'education': 'Aditya is a B.Tech Computer Science & Engineering student at Heritage Institute of Technology, Kolkata, with a CGPA of 9.2+.',
            'achievements': 'Aditya has won the Hactonix Hackathon, was 2nd Runner-Up at Hack Heritage, and was a finalist in Smart India Hackathon 2024.',
            'experience': 'Aditya has experience in machine learning, deep learning, web development, and has led technical teams in various hackathons.',
            'github': 'You can check out Aditya\'s projects on GitHub at https://github.com/adityachaudharycode',
            'linkedin': 'Connect with Aditya on LinkedIn: https://www.linkedin.com/in/aditya-chaudhary-730a1432a'
        };
        
        // Check for keywords in the message
        for (let keyword in responses) {
            if (message.includes(keyword)) {
                return responses[keyword];
            }
        }
        
        // Default responses
        const defaultResponses = [
            'That\'s interesting! Could you tell me more about what you\'d like to know about Aditya?',
            'I\'d be happy to help! You can ask me about Aditya\'s projects, skills, achievements, or contact information.',
            'Feel free to ask me anything about Aditya\'s background, projects, or how to get in touch with him!',
            'I\'m here to help! What specific information about Aditya would you like to know?'
        ];
        
        return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
    }
    
    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
    
    updateNotification() {
        if (this.messageCount > 1 && !this.isOpen) {
            this.chatNotification.textContent = this.messageCount - 1;
            this.chatNotification.style.display = 'flex';
        } else {
            this.chatNotification.style.display = 'none';
        }
    }
    
    // Method to integrate with external chatbot API
    async integrateWithChatbot(message) {
        // This is where you would integrate with your preferred chatbot service
        // Example integrations:
        
        // OpenAI GPT
        // const response = await fetch('https://api.openai.com/v1/chat/completions', {...});
        
        // Google Dialogflow
        // const response = await fetch('https://dialogflow.googleapis.com/v2/projects/...', {...});
        
        // Custom API
        // const response = await fetch('your-chatbot-api-endpoint', {...});
        
        // For now, return the simple response
        return this.generateResponse(message.toLowerCase());
    }
}

// Initialize chat widget when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ChatWidget();
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatWidget;
}

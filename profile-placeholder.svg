<svg width="300" height="300" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="150" cy="150" r="150" fill="url(#grad1)"/>
  
  <!-- Profile Icon -->
  <g transform="translate(150,150)">
    <!-- Head -->
    <circle cx="0" cy="-30" r="35" fill="white" opacity="0.9"/>
    
    <!-- Body -->
    <path d="M -50 40 Q -50 10 -35 10 L 35 10 Q 50 10 50 40 L 50 80 L -50 80 Z" fill="white" opacity="0.9"/>
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="80" cy="80" r="8" fill="white" opacity="0.3"/>
  <circle cx="220" cy="100" r="6" fill="white" opacity="0.4"/>
  <circle cx="250" cy="200" r="10" fill="white" opacity="0.2"/>
  <circle cx="60" cy="220" r="7" fill="white" opacity="0.3"/>
</svg>

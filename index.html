<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Portfolio</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loader" id="loader">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <div class="loader-text">Loading Portfolio...</div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text">Portfolio</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">About</a>
                </li>
                <li class="nav-item">
                    <a href="#projects" class="nav-link">Projects</a>
                </li>
                <li class="nav-item">
                    <a href="#skills" class="nav-link">Skills</a>
                </li>
                <li class="nav-item">
                    <a href="#achievements" class="nav-link">Achievements</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">Contact</a>
                </li>
                <li class="nav-item">
                    <button class="theme-toggle" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="particles-container">
            <canvas id="particles-canvas"></canvas>
        </div>
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">
                    <span class="typing-text">Hello, I'm</span>
                    <span class="name-highlight">Aditya Chaudhary</span>
                </h1>
                <p class="hero-subtitle">Deep Learning , ML Enthusiast & Web Dev | B.Tech CSE Student</p>
                <div class="hero-buttons">
                    <a href="#projects" class="btn btn-primary">View My Work</a>
                    <a href="#contact" class="btn btn-secondary">Get In Touch</a>
                </div>
            </div>
            <div class="hero-image">
                <div class="floating-card">
                    <div class="card-content">
                        <i class="fas fa-brain"></i>
                        <span>Machine Learning</span>
                    </div>
                </div>
                <div class="floating-card card-2">
                    <div class="card-content">
                        <i class="fas fa-robot"></i>
                        <span>Deep Learning</span>
                    </div>
                </div>
                <div class="floating-card card-3">
                    <div class="card-content">
                        <i class="fas fa-mobile-alt"></i>
                        <span>Web Development</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="scroll-indicator">
            <div class="scroll-arrow"></div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">About Me</h2>
                <p class="section-subtitle">Deep learning and ML enthusiast who enjoys building impactful projects and leading technical teams to success</p>
            </div>
            <div class="about-content">
                <div class="about-text">
                    <p class="about-description">
                        I'm a B.Tech Computer Science & Engineering student at Heritage Institute of Technology, Kolkata,
                        with a CGPA of 9.2+. As a deep learning and ML enthusiast, I enjoy building impactful projects
                        that solve real-world problems. I'm passionate about leading technical teams and always strive
                        to grow and learn new technologies.
                    </p>
                    <div class="about-stats">
                        <div class="stat-item">
                            <span class="stat-number" data-target="4">0</span>
                            <span class="stat-label">Major Projects</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" data-target="2">0</span>
                            <span class="stat-label">Hackathon Wins</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" data-target="9.2+">0</span>
                            <span class="stat-label">CGPA (x10)</span>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <div class="image-container">
                        <div class="profile-picture">
                            <img src="profile-placeholder.svg" alt="Aditya Chaudhary - Profile Picture" class="profile-img" id="profileImg">
                            <div class="profile-overlay">
                                <div class="profile-border"></div>
                            </div>
                        </div>
                        <div class="floating-elements">
                            <div class="floating-element element-1"></div>
                            <div class="floating-element element-2"></div>
                            <div class="floating-element element-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">My Projects</h2>
                <p class="section-subtitle">Here are some of my recent works</p>
            </div>
            <div class="projects-grid">
                <div class="project-card">
                    <div class="project-image">
                        <img src="plant.png" alt="Plant Disease Detection Project" class="project-img">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                                <a href="https://github.com/adityachaudharycode/Plant_disease_Detection" class="project-link"><i class="fab fa-github"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">Plant Disease Detection</h3>
                        <p class="project-description">Deep learning model based web app for real-time plant disease prediction using Streamlit with various classes and real-life data</p>
                        <div class="project-tech">
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">Streamlit</span>
                            <span class="tech-tag">TensorFlow</span>
                            <span class="tech-tag">Deep Learning</span>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-image">
                        <img src="krishisahayak.png" alt="KrishiSahayak Project" class="project-img">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="https://krishisahayak.xyz/" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                                <a href="#" class="project-link"><i class="fab fa-github"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">KrishiSahayak</h3>
                        <p class="project-description">Comprehensive webapp and Android app for livestock disease detection with multilingual support, voice search, and AI-powered symptom analysis</p>
                        <div class="project-tech">
                            <span class="tech-tag">Android Studio</span>
                            <span class="tech-tag">Machine Learning</span>
                            <span class="tech-tag">Firebase</span>
                            <span class="tech-tag">AI Integration</span>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-image">
                        <img src="empathy.png" alt="Empathy Bot Project" class="project-img">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="https://empathy-bot.onrender.com/" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                                <a href="https://github.com/adityachaudharycode/Empathy_Bot" class="project-link"><i class="fab fa-github"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">Empathy Bot</h3>
                        <p class="project-description">An empathetic chatbot for introverts to express feelings, built with Google Gemini API for compassionate responses</p>
                        <div class="project-tech">
                            <span class="tech-tag">Google Gemini API</span>
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">NLP</span>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-image">
                        <img src="mentor.png" alt="MentorVerse Project" class="project-img">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="https://adityachaudharycode.github.io/Mentor_verse/" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                                <a href="https://adityachaudharycode.github.io/Mentor_verse/" class="project-link"><i class="fab fa-github"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">MentorVerse</h3>
                        <p class="project-description">Platform connecting mentors and mentees with real-time chat, user authentication, and AI-powered MentorBot for instant query resolution</p>
                        <div class="project-tech">
                            <span class="tech-tag">WebSocket</span>
                            <span class="tech-tag">User Authentication</span>
                            <span class="tech-tag">AI Chatbot</span>
                            <span class="tech-tag">Real-time Chat</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Skills & Technologies</h2>
                <p class="section-subtitle">Technologies I work with</p>
            </div>
            <div class="skills-content">
                <div class="skills-categories">
                    <div class="skill-category">
                        <h3 class="category-title">Programming Languages</h3>
                        <div class="skill-items">
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">Python</span>
                                    <span class="skill-percentage">95%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="95"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">JavaScript</span>
                                    <span class="skill-percentage">85%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="85"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">C/C++</span>
                                    <span class="skill-percentage">80%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="80"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">Java</span>
                                    <span class="skill-percentage">75%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="75"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="skill-category">
                        <h3 class="category-title">Frameworks & Technologies</h3>
                        <div class="skill-items">
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">TensorFlow</span>
                                    <span class="skill-percentage">90%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="90"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">Flask/Streamlit</span>
                                    <span class="skill-percentage">85%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="85"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">Android Studio</span>
                                    <span class="skill-percentage">80%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="80"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">Git & GitHub</span>
                                    <span class="skill-percentage">85%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="85"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="skill-category">
                    <h3 class="category-title">Domains & Databases</h3>
                    <div class="skill-items">
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Machine Learning</span>
                                <span class="skill-percentage">90%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="90"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Deep Learning</span>
                                <span class="skill-percentage">85%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="85"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Firebase</span>
                                <span class="skill-percentage">80%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="80"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">MongoDB/SQL</span>
                                <span class="skill-percentage">75%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="75"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Achievements Section -->
    <section id="achievements" class="achievements">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Achievements</h2>
                <p class="section-subtitle">Recognition and awards for technical excellence</p>
            </div>
            <div class="achievements-grid">
                <div class="achievement-card">
                    <div class="achievement-image">
                        <img src="hacktonix.png" alt="Hactonix Hackathon" class="achievement-img">
                        <div class="achievement-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                    </div>
                    <div class="achievement-content">
                        <h3 class="achievement-title">Winner</h3>
                        <p class="achievement-event">Hactonix Hackathon</p>
                        <p class="achievement-location">Future Institute of Engineering and Management, Kolkata</p>
                    </div>
                </div>
                <div class="achievement-card">
                    <div class="achievement-image">
                        <img src="hackheritage.png" alt="Hack Heritage" class="achievement-img">
                        <div class="achievement-icon">
                            <i class="fas fa-medal"></i>
                        </div>
                    </div>
                    <div class="achievement-content">
                        <h3 class="achievement-title">2nd Runner-Up</h3>
                        <p class="achievement-event">Hack Heritage</p>
                        <p class="achievement-location">Heritage Institute of Technology, Kolkata</p>
                    </div>
                </div>
                <div class="achievement-card">
                    <div class="achievement-image">
                        <img src="sih.png" alt="Smart India Hackathon" class="achievement-img">
                        <div class="achievement-icon">
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <div class="achievement-content">
                        <h3 class="achievement-title">SIH 2024 Finalist</h3>
                        <p class="achievement-event">Smart India Hackathon</p>
                        <p class="achievement-location">Integral University, Lucknow</p>
                    </div>
                </div>
                <div class="achievement-card">
                    <div class="achievement-image">
                        <img src="aignite.png" alt="Aignite'25" class="achievement-img">
                        <div class="achievement-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="achievement-content">
                        <h3 class="achievement-title">Participant</h3>
                        <p class="achievement-event">Aignite'25</p>
                        <p class="achievement-location">East India's Largest AI Hackathon, Kolkata</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Get In Touch</h2>
                <p class="section-subtitle">Let's work together on your next project</p>
            </div>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h4>Email</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-details">
                            <h4>Phone</h4>
                            <p>+91 7992466682</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-details">
                            <h4>Location</h4>
                            <p>Kolkata, India</p>
                        </div>
                    </div>
                </div>
                <form class="contact-form">
                    <h1>Any feedback or want to contact </h1>
                    <div class="form-group">
                        <input type="text" id="name" name="name" placeholder="Your Name" required>
                    </div>
                    <div class="form-group">
                        <input type="email" id="email" name="email" placeholder="Your Email" required>
                    </div>
                    <div class="form-group">
                        <input type="text" id="subject" name="subject" placeholder="Subject" required>
                    </div>
                    <div class="form-group">
                        <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary" style="color: black;">Send Message</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2025 Aditya Chaudhary. All rights reserved.</p>
                <div class="social-links">
                    <a href="https://github.com/adityachaudharycode" class="social-link"><i class="fab fa-github"></i></a>
                    <a href="https://www.linkedin.com/in/aditya-chaudhary-730a1432a" class="social-link"><i class="fab fa-linkedin"></i></a>
                    <a href="https://x.com/aditya_ch1801?t=z3MIM5xzfH_o22bQpjsStg&s=09" class="social-link"><i class="fab fa-twitter"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Chat Button -->
    <div class="chat-container">
        <div class="chat-widget" id="chatWidget">
            <div class="chat-header">
                <div class="chat-header-content">
                    <div class="chat-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="chat-info">
                        <h4>AI Assistant</h4>
                        <span class="chat-status">Online</span>
                    </div>
                </div>
                <button class="chat-close" id="chatClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="chat-messages" id="chatMessages">
                <!-- Messages will be dynamically added here -->
            </div>
            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <input type="text" id="chatInput" placeholder="Type your message..." autocomplete="off">
                    <button class="chat-send" id="chatSend">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
        <button class="chat-toggle" id="chatToggle">
            <i class="fas fa-comments"></i>
            <span class="chat-notification" id="chatNotification">1</span>
        </button>
    </div>

    <script src="scripts/firebase-config.js"></script>
    <script src="scripts/main.js"></script>
    <script src="scripts/chat.js"></script>
</body>
</html>
